import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import sys
import io
import os
import argparse
import time
from tqdm import tqdm

# 解决控制台输出编码问题
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 尝试导入TensorFlow
try:
    import tensorflow as tf
    from tensorflow.keras.models import Model
    from tensorflow.keras.layers import Dense, Input, Concatenate, BatchNormalization, Dropout, LSTM, Flatten
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
    USE_TENSORFLOW = True
    print("成功加载TensorFlow。")
except ImportError:
    USE_TENSORFLOW = False
    print("警告：未能加载TensorFlow。")

# ==============================================================================
# 1. 平台运动与几何关系 (Platform Motion and Geometry)
# ==============================================================================

def weave(g, T, del_T, alt_kft, vel):
    """(保持不变) 平台导航数据生成函数"""
    dt = del_T
    dtt = del_T/20
    accelh = g * 9.81
    alt0 = alt_kft * 1000 * 0.3048
    dtr = np.pi / 180
    maxturn = 30 * dtr
    vhoriz = vel
    radturn = (vhoriz**2) / accelh
    turnrate = vel / radturn
    turndur = 2 * maxturn / turnrate
    turndur = np.floor(turndur/dtt) * dtt
    px, py, pz = 0, 0, alt0
    XYZA, XYZADOT = [], []
    itt = 0
    for tt in np.arange(0, T + dtt, dtt):
        trem = tt % (2 * turndur)
        if trem < turndur:
            turnangl = maxturn - turnrate * trem
        else:
            turnangl = -maxturn + turnrate * (trem - turndur)
        vx = vhoriz * np.cos(turnangl)
        vy = -vhoriz * np.sin(turnangl)
        if itt > 0:
            vz = XYZADOT[-1][2]
            px += vx * dtt
            py += vy * dtt
        else:
            vz = 0
        if abs(np.mod(itt * dtt, dt)) < 1e-6:
            XYZA.append([px, py, pz])
            XYZADOT.append([vx, vy, vz])
        itt += 1
    XYZA = np.array(XYZA).T
    XYZADOT = np.array(XYZADOT).T
    hdg = np.pi/2 - np.arctan2(XYZADOT[1, :], XYZADOT[0, :])
    long_vect = np.vstack([np.sin(hdg), np.cos(hdg), np.zeros_like(hdg)])
    vect_norms = np.sqrt(np.sum(long_vect**2, axis=0))
    long_vect = long_vect / (vect_norms + 1e-9)
    return XYZA[0, :], XYZA[1, :], XYZA[2, :], XYZADOT[0, :], XYZADOT[1, :], XYZADOT[2, :], long_vect

# ==============================================================================
# 2. IQ数据生成与信号处理 (IQ Data Generation and Signal Processing)
# ==============================================================================

def generate_iq_data(p_true, platform_pos, platform_vel, mu_vect_single, fo, snr_db, L=2, d=0.5, fs=2e4, num_samples=2048):
    """(保持不变) 生成只包含核心物理效应的IQ数据"""
    c = 2.998e8
    xe, ye, ze, fo_true = p_true
    Px, Py, Pz = platform_pos
    R_vec = np.array([Px - xe, Py - ye, Pz - ze])
    R = np.linalg.norm(R_vec)
    los_vec_norm = R_vec / (R + 1e-9)
    relative_velocity = np.dot(platform_vel, los_vec_norm)
    doppler_freq = -(fo_true / c) * relative_velocity
    cos_theta = -np.dot(los_vec_norm, mu_vect_single)
    t = np.arange(num_samples) / fs
    signal_power = 1.0
    iq_data = np.zeros((L, num_samples), dtype=np.complex128)
    for i in range(L):
        # 假设 d 是以波长为单位的间距，例如0.5代表半波长
        phase_shift = (2 * np.pi * d) * cos_theta
        signal = np.exp(1j * (2 * np.pi * doppler_freq * t + phase_shift * i))
        iq_data[i, :] = signal
    snr_linear = 10**(snr_db / 10.0)
    noise_power = signal_power / snr_linear
    noise_std = np.sqrt(noise_power / 2)
    noise = (np.random.normal(0, noise_std, size=iq_data.shape) + 
             1j * np.random.normal(0, noise_std, size=iq_data.shape))
    return iq_data + noise

def extract_features_from_iq(iq_data, fo, fs):
    """(保持不变) 提取稳定、无模糊的特征"""
    L, num_samples = iq_data.shape
    avg_signal = np.mean(iq_data, axis=0)
    fft_result = np.fft.fft(avg_signal)
    fft_freqs = np.fft.fftfreq(num_samples, 1/fs)
    peak_index = np.argmax(np.abs(fft_result))
    doppler_est = fft_freqs[peak_index]
    f_est = fo + doppler_est
    if L >= 2:
        cross_correlation = np.mean(iq_data[1, :] * np.conj(iq_data[0, :]))
        norm_factor = np.abs(cross_correlation)
        normalized_correlation = cross_correlation / (norm_factor + 1e-9)
        corr_real = normalized_correlation.real
        corr_imag = normalized_correlation.imag
    else:
        corr_real, corr_imag = 0.0, 0.0
    return f_est, corr_real, corr_imag

# ==============================================================================
# 3. 传统求解器 (Traditional Solver - Ported from MATLAB)
# ==============================================================================

def traditional_solver(measurement_obs, Plat_Nav_Data, mu_vect, fo, L=1.0, d=0.5):
    """
    (已修正) Python实现的传统迭代最小二乘法求解器, 修正了 'd' 未定义的错误。
    """
    c = 2.998e8
    lambda_ = c / fo
    f_obs = measurement_obs[:, 0]
    
    # 从特征反推phi，用于传统方法
    corr_real = measurement_obs[:, 1]
    corr_imag = measurement_obs[:, 2]
    phase_diff = np.arctan2(corr_imag, corr_real)
    
    # 'd' (天线间距，波长倍数) 现在作为函数参数传入
    phi_obs = -phase_diff * (L / (d*lambda_)) if d > 0 else np.zeros_like(f_obs)

    # 简化的精度估计
    freq_accuracy = 10.0  # Hz
    phi_accuracy = 0.1   # radians
    
    Px, Py, Pz, Vx, Vy, Vz = Plat_Nav_Data
    
    # 初始估计
    p_est = np.array([np.mean(Px), np.mean(Py), 0, fo])

    for _ in range(15): # 迭代次数
        xe_hat, ye_hat, ze_hat, fo_hat = p_est
        
        # 1. 计算预测值和残差
        diff_X, diff_Y, diff_Z = Px - xe_hat, Py - ye_hat, Pz - ze_hat
        R_hat = np.sqrt(diff_X**2 + diff_Y**2 + diff_Z**2 + 1e-9)
        
        f_hat = fo_hat - (fo_hat / c) * (Vx * diff_X + Vy * diff_Y + Vz * diff_Z) / R_hat
        res_f = f_obs - f_hat

        phi_hat = -(2 * np.pi / lambda_) * (mu_vect[0, :] * diff_X + mu_vect[1, :] * diff_Y + mu_vect[2, :] * diff_Z) / R_hat
        res_phi = phi_obs - phi_hat

        # 2. 计算雅可比矩阵 H
        H_dop = np.zeros((len(Px), 4))
        H_aoa = np.zeros((len(Px), 4))

        eps = 1e-4
        for i in range(4):
            p_plus = p_est.copy(); p_plus[i] += eps
            p_minus = p_est.copy(); p_minus[i] -= eps
            
            f_plus, phi_plus = calculate_measurements_for_jacobian(p_plus, Plat_Nav_Data, mu_vect, fo, d=d)
            f_minus, phi_minus = calculate_measurements_for_jacobian(p_minus, Plat_Nav_Data, mu_vect, fo, d=d)

            H_dop[:, i] = (f_plus - f_minus) / (2 * eps)
            H_aoa[:, i] = (phi_plus - phi_minus) / (2 * eps)

        # 3. 求解更新量 del_p
        W_dop = np.eye(len(f_obs)) / freq_accuracy**2
        W_aoa = np.eye(len(phi_obs)) / phi_accuracy**2
        
        A = H_dop.T @ W_dop @ H_dop + H_aoa.T @ W_aoa @ H_aoa
        b = H_dop.T @ W_dop @ res_f + H_aoa.T @ W_aoa @ res_phi
        
        try:
            del_p = np.linalg.pinv(A) @ b
        except np.linalg.LinAlgError:
            break
        
        if np.linalg.norm(del_p) > 5000: break

        p_est += 0.5 * del_p 

        if np.linalg.norm(del_p) < 1.0: break
            
    return p_est[:3]

def calculate_measurements_for_jacobian(params, Plat_Nav_Data, mu_vect, fo, L=1.0, d=0.5):
    """为雅可比矩阵计算辅助函数"""
    c = 2.998e8; lambda_ = c/fo
    xe, ye, ze, fo_est = params
    Px, Py, Pz, Vx, Vy, Vz = Plat_Nav_Data
    R = np.sqrt((Px - xe)**2 + (Py - ye)**2 + (Pz - ze)**2 + 1e-9)
    f = fo_est - (fo_est / c) * (Vx * (Px - xe) + Vy * (Py - ye) + Vz * (Pz - ze)) / R
    cos_theta = -np.dot(mu_vect.T, ((Plat_Nav_Data[:3].T - params[:3]) / R[:, np.newaxis]).T).diagonal()
    phase_diff = (2 * np.pi * d) * cos_theta
    phi = -phase_diff * (L / (d*lambda_)) if d > 0 else np.zeros_like(f)
    return f, phi

# ==============================================================================
# 4. 混合循环神经网络模型 (Hybrid Recurrent Neural Network)
# ==============================================================================

def build_recurrent_model(seq_len, num_features, traditional_input_shape=3):
    """(保持不变) 混合RNN模型"""
    sequence_input = Input(shape=(seq_len, num_features), name='sequence_input')
    traditional_input = Input(shape=(traditional_input_shape,), name='traditional_input')
    lstm_out = LSTM(128, return_sequences=False)(sequence_input)
    lstm_out = BatchNormalization()(lstm_out)
    lstm_out = Dropout(0.3)(lstm_out)
    combined = Concatenate()([lstm_out, traditional_input])
    x = Dense(128, activation='relu')(combined)
    x = BatchNormalization()(x)
    x = Dropout(0.3)(x)
    x = Dense(64, activation='relu')(x)
    output = Dense(3, name='position_output')(x)
    model = Model(inputs=[sequence_input, traditional_input], outputs=output)
    optimizer = Adam(learning_rate=0.001)
    model.compile(optimizer=optimizer, loss='huber', metrics=['mae'])
    return model


def generate_sequence_training_data(num_scenarios=2000, scene_size=2000, snr_range=(12, 25)):
    """(已修正) 生成序列化的训练数据"""
    print(f"正在生成 {num_scenarios} 个序列化的训练场景...")
    X_seq, X_trad, Y_pos = [], [], []
    fo = 1e9
    
    for _ in tqdm(range(num_scenarios), desc="生成训练数据"):
        g = np.random.uniform(1.5, 3.0)
        T = np.random.uniform(10, 20)
        del_T = 0.2
        alt_kft = np.random.uniform(8, 12)
        vel = np.random.uniform(180, 250)
        snr_db = np.random.uniform(*snr_range)

        x_true = np.random.uniform(-scene_size/2, scene_size/2)
        y_true = np.random.uniform(-scene_size/2, scene_size/2)
        z_true = np.random.uniform(0, scene_size)
        p_true = np.array([x_true, y_true, z_true, fo])

        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
        Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])
        
        measurement_obs = []
        for j in range(len(Px)):
            platform_pos = np.array([Px[j], Py[j], Pz[j]])
            platform_vel = np.array([Vx[j], Vy[j], Vz[j]])
            mu_vect_single = mu_vect[:, j]
            iq_data = generate_iq_data(p_true, platform_pos, platform_vel, mu_vect_single, fo, snr_db)
            f, cr, ci = extract_features_from_iq(iq_data, fo, fs=2e4)
            measurement_obs.append([f, cr, ci])
        measurement_obs = np.array(measurement_obs)

        # 此处是关键修正：在调用 traditional_solver 时，明确传递 d 的值
        p_trad = traditional_solver(measurement_obs, Plat_Nav_Data, mu_vect, fo, d=0.5)
        
        X_seq.append(measurement_obs)
        X_trad.append(p_trad)
        Y_pos.append(p_true[:3])

    return np.array(X_seq), np.array(X_trad), np.array(Y_pos)

def train_recurrent_model(X_seq, X_trad, Y_pos, epochs=100, batch_size=64):
    """(保持不变) 训练混合RNN模型"""
    seq_mean = np.mean(X_seq.reshape(-1, X_seq.shape[-1]), axis=0)
    seq_std = np.std(X_seq.reshape(-1, X_seq.shape[-1]), axis=0)
    X_seq_norm = (X_seq - seq_mean) / (seq_std + 1e-8)

    trad_mean = X_trad.mean(axis=0)
    trad_std = X_trad.std(axis=0)
    X_trad_norm = (X_trad - trad_mean) / (trad_std + 1e-8)

    pos_mean = Y_pos.mean(axis=0)
    pos_std = Y_pos.std(axis=0)
    Y_pos_norm = (Y_pos - pos_mean) / (pos_std + 1e-8)

    norm_params = {
        'seq_mean': seq_mean, 'seq_std': seq_std,
        'trad_mean': trad_mean, 'trad_std': trad_std,
        'pos_mean': pos_mean, 'pos_std': pos_std,
    }
    np.save('nn_normalization_params_v5.npy', norm_params)
    print("归一化参数已保存到 nn_normalization_params_v5.npy")

    model = build_recurrent_model(X_seq.shape[1], X_seq.shape[2])
    model.summary()

    callbacks = [
        EarlyStopping(monitor='val_loss', patience=15, restore_best_weights=True, verbose=1),
        ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=7, min_lr=1e-7, verbose=1),
        ModelCheckpoint('best_location_model_v5.keras', monitor='val_loss', save_best_only=True)
    ]

    print("开始训练混合RNN模型...")
    model.fit(
        [X_seq_norm, X_trad_norm],
        Y_pos_norm,
        validation_split=0.2,
        epochs=epochs,
        batch_size=batch_size,
        callbacks=callbacks,
        verbose=1,
        shuffle=True
    )
    print("模型训练完成。")


# ==============================================================================
# 5. 主流程、绘图与精度评估
# ==============================================================================

def run_accuracy_test(num_tests=200, snr_db=12, scene_size=2000):
    """(已修正) 运行精度评估测试"""
    model_path = 'best_location_model_v5.keras'
    norm_path = 'nn_normalization_params_v5.npy'
    if not os.path.exists(model_path):
        print(f"错误：找不到模型 '{model_path}'。请先使用 --train 参数训练。")
        return

    print("加载预训练的混合RNN模型...")
    model = tf.keras.models.load_model(model_path)
    norm_params = np.load(norm_path, allow_pickle=True).item()

    errors_trad, errors_hybrid = [], []
    successful_trad, successful_hybrid = 0, 0

    for _ in tqdm(range(num_tests), desc="精度评估中"):
        g = np.random.uniform(1.5, 3.0); T = np.random.uniform(10, 20); del_T = 0.2
        alt_kft = np.random.uniform(8, 12); vel = np.random.uniform(180, 250); fo = 1e9
        
        x_true = np.random.uniform(-scene_size/2, scene_size/2)
        y_true = np.random.uniform(-scene_size/2, scene_size/2)
        z_true = np.random.uniform(0, scene_size)
        p_true = np.array([x_true, y_true, z_true, fo])
        
        Px, Py, Pz, Vx, Vy, Vz, mu_vect = weave(g, T, del_T, alt_kft, vel)
        Plat_Nav_Data = np.vstack([Px, Py, Pz, Vx, Vy, Vz])
        measurement_obs = []
        for j in range(len(Px)):
            platform_pos = np.array([Px[j], Py[j], Pz[j]])
            platform_vel = np.array([Vx[j], Vy[j], Vz[j]])
            mu_vect_single = mu_vect[:, j]
            iq_data = generate_iq_data(p_true, platform_pos, platform_vel, mu_vect_single, fo, snr_db)
            f, cr, ci = extract_features_from_iq(iq_data, fo, fs=2e4)
            measurement_obs.append([f, cr, ci])
        measurement_obs = np.array(measurement_obs)

        # 1. 传统方法预测
        # 此处是关键修正：在调用 traditional_solver 时，明确传递 d 的值
        p_trad = traditional_solver(measurement_obs, Plat_Nav_Data, mu_vect, fo, d=0.5)
        
        # 2. 混合RNN方法预测
        seq_norm = (measurement_obs - norm_params['seq_mean']) / (norm_params['seq_std'] + 1e-8)
        trad_norm = (p_trad - norm_params['trad_mean']) / (norm_params['trad_std'] + 1e-8)
        p_hybrid_norm = model.predict([np.expand_dims(seq_norm, axis=0), np.expand_dims(trad_norm, axis=0)], verbose=0)
        p_hybrid = p_hybrid_norm * norm_params['pos_std'] + norm_params['pos_mean']
        p_hybrid = p_hybrid.flatten()

        error_trad = np.linalg.norm(p_trad - p_true[:3])
        error_hybrid = np.linalg.norm(p_hybrid - p_true[:3])
        errors_trad.append(error_trad)
        errors_hybrid.append(error_hybrid)

        true_distance = np.linalg.norm(p_true[:3])
        error_threshold = max(100.0, 0.05 * true_distance)
        if error_trad <= error_threshold: successful_trad += 1
        if error_hybrid <= error_threshold: successful_hybrid += 1
    
    print("\n" + "="*50)
    print(" 精  度  评  估  报  告 (v5 - Hybrid RNN)")
    print("="*50)
    print(f"场景大小: {scene_size}m, 信噪比: {snr_db}dB, 测试次数: {num_tests}")
    print("-" * 50)
    print(f"传统方法: 准确率 {successful_trad/num_tests:.2%}, 平均误差 {np.mean(errors_trad):.2f}m")
    print(f"混合RNN:  准确率 {successful_hybrid/num_tests:.2%}, 平均误差 {np.mean(errors_hybrid):.2f}m")
    print("-" * 50)
    if (successful_hybrid/num_tests) >= 0.85:
        print("✅ 目标达成：混合RNN模型定位准确率不低于85%。")
    else:
        print("❌ 目标未达成：混合RNN模型定位准确率低于85%。")
    print("="*50)

    plt.figure(figsize=(12, 6))
    plt.hist(errors_trad, bins=50, alpha=0.6, label=f'传统方法 (均值: {np.mean(errors_trad):.0f}m)', color='royalblue')
    plt.hist(errors_hybrid, bins=50, alpha=0.8, label=f'混合RNN (均值: {np.mean(errors_hybrid):.0f}m)', color='darkorange')
    plt.title(f'误差分布对比 (SNR={snr_db}dB)')
    plt.xlabel('定位误差 (m)'); plt.ylabel('次数'); plt.legend(); plt.grid(True, alpha=0.3)
    plt.savefig('accuracy_test_error_distribution_v5.png')
    plt.show()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='基于IQ数据的辐射源定位系统 (v5 - 混合RNN版)')
    parser.add_argument('--train', action='store_true', help='强制重新训练模型。')
    parser.add_argument('--test-accuracy', action='store_true', help='运行精度评估测试。')
    parser.add_argument('--snr', type=float, default=12.0, help='设置测试的信噪比(dB)。')
    parser.add_argument('--num-tests', type=int, default=200, help='设置精度评估的测试次数。')

    args = parser.parse_args()

    if not USE_TENSORFLOW:
        sys.exit("错误：此脚本需要TensorFlow。")

    if args.train:
        print("开始训练流程...")
        X_seq, X_trad, Y_pos = generate_sequence_training_data(num_scenarios=4000)
        train_recurrent_model(X_seq, X_trad, Y_pos, epochs=120, batch_size=64)
    
    elif args.test_accuracy:
        run_accuracy_test(num_tests=args.num_tests, snr_db=args.snr)
        
    else:
        print("此脚本的主要功能是训练(--train)和评估(--test-accuracy)。")
        print("要运行单次定位演示，请取消注释或修改主函数部分的代码。")
